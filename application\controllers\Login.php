<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends CI_Controller {

	public function __construct()
    {
        parent::__construct();
        $this->load->helper('url');
        //load library form validasi
        $this->load->library('form_validation');
        //load model admin
        $this->load->model('admin');
        $this->output->disable_cache();
    }

    public function index()
    {
      $this->load->view('login');
    }
    
    function auth_db(){
      $username = $this->input->post("username", TRUE);
      $password = md5($this->input->post("password", TRUE));
      $checking = $this->admin->check_login('admin',$username,$password);
            //jika ditemukan, maka create session
            if ($checking != FALSE) {
                foreach ($checking as $apps) {
                    if($apps->role == 'admin'){
                        if($apps->password == $password){
                            $session_data = array(
                                'id_admin'  => $apps->id_admin,
                                'username'  => $apps->username,
                                'nama'      => $apps->nama,
                                'role'      => $apps->role,
                            );
                            $this->session->set_userdata($session_data);
                            $this->session->set_flashdata('msg', 'berhasil login!');
                            redirect('dashboard');
                        }
                        else{
                            $this->session->set_flashdata('err', 'err');
                            redirect('login');
                        }
                    }
                //TAMBAH ROLE
                else{
                    $this->session->set_flashdata('err', 'role kode unit belum ditambahkan');
                    redirect('login');
                }
            }
        }
        else{
            $this->session->set_flashdata('err', 'err');
            redirect('login');
        }
  }

    public function logout()
    {
        $this->session->sess_destroy();
        $this->session->set_flashdata('msg', 'berhasil logout!');
        redirect('login');
    }

}
