    
    <div class="w3-row-padding w3-stretch" style="margin-top: 100px">
        <div class="w3-col m1">
            <div class="w3-container"></div>
        </div>
        <div class="w3-col m10">
            <!-- Welcome Section -->
            <div class="w3-card w3-round-large w3-center w3-white" style="margin-bottom: 2rem; padding: 2rem; border-left: 4px solid #dc2626;">
                <h1 style="color: #1f2937; margin: 0; font-weight: 600;">
                    <i class="fas fa-chart-line" style="margin-right: 0.5rem; color: #dc2626;"></i>
                    Dashboard TRUST
                </h1>
                <p style="color: #6b7280; margin: 0.5rem 0 0 0; font-size: 1.1rem;">Tracking Surat Terpadu - Analitik Surat Real-time</p>
                <div style="margin-top: 1rem; color: #6b7280; font-size: 0.9rem;">
                    <i class="fas fa-sync-alt" id="refresh-indicator" style="margin-right: 0.5rem; color: #dc2626;"></i>
                    <span id="last-update">Data diperbarui: <?= date('d/m/Y H:i:s') ?></span>
                </div>
            </div>

            <!-- Quick Stats Row -->
            <div class="w3-row-padding w3-stretch" style="margin-bottom: 2rem;">
                <div class="w3-col m3">
                    <div class="w3-card w3-round-large w3-center w3-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 1.5rem;">
                        <i class="fas fa-calendar-day" style="font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.9;"></i>
                        <h3 style="margin: 0; font-weight: 600;">Hari Ini</h3>
                        <h1 style="margin: 0.5rem 0 0 0; font-size: 2rem; font-weight: 700;"><?= isset($surat_hari_ini) ? $surat_hari_ini : 0; ?></h1>
                        <p style="margin: 0.25rem 0 0 0; opacity: 0.9; font-size: 0.8rem;">Surat</p>
                    </div>
                </div>
                <div class="w3-col m3">
                    <div class="w3-card w3-round-large w3-center w3-white" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); color: white; padding: 1.5rem;">
                        <i class="fas fa-calendar-week" style="font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.9;"></i>
                        <h3 style="margin: 0; font-weight: 600;">Minggu Ini</h3>
                        <h1 style="margin: 0.5rem 0 0 0; font-size: 2rem; font-weight: 700;"><?= isset($surat_minggu_ini) ? $surat_minggu_ini : 0; ?></h1>
                        <p style="margin: 0.25rem 0 0 0; opacity: 0.9; font-size: 0.8rem;">Surat</p>
                    </div>
                </div>
                <div class="w3-col m3">
                    <div class="w3-card w3-round-large w3-center w3-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white; padding: 1.5rem;">
                        <i class="fas fa-calendar-alt" style="font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.9;"></i>
                        <h3 style="margin: 0; font-weight: 600;">Bulan Ini</h3>
                        <h1 style="margin: 0.5rem 0 0 0; font-size: 2rem; font-weight: 700;"><?= isset($surat_bulan_ini) ? $surat_bulan_ini : 0; ?></h1>
                        <p style="margin: 0.25rem 0 0 0; opacity: 0.9; font-size: 0.8rem;">Surat</p>
                    </div>
                </div>
                <div class="w3-col m3">
                    <div class="w3-card w3-round-large w3-center w3-white" style="background: #1f2937; color: white; padding: 1.5rem; border-top: 4px solid #dc2626;">
                        <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 0.5rem; opacity: 0.9;"></i>
                        <h3 style="margin: 0; font-weight: 600;">Total</h3>
                        <h1 style="margin: 0.5rem 0 0 0; font-size: 2rem; font-weight: 700;"><?= $total_sm; ?></h1>
                        <p style="margin: 0.25rem 0 0 0; opacity: 0.9; font-size: 0.8rem;">Seluruh surat</p>
                    </div>
                </div>
            </div>

            <div class="w3-row-padding w3-stretch">
                <!-- Charts Section -->
                <div class="w3-col m8">
                    <!-- Monthly Trend Chart -->
                    <div class="w3-card w3-round-large w3-white" style="margin-bottom: 1.5rem; padding: 1.5rem;">
                        <h3 style="color: #1f2937; margin: 0 0 1.5rem 0; font-weight: 600;">
                            <i class="fas fa-chart-area" style="margin-right: 0.5rem; color: #dc2626;"></i>
                            Trend Surat (6 Bulan Terakhir)
                        </h3>
                        <canvas id="monthlyChart" style="max-height: 300px;"></canvas>
                    </div>
                    
                    <!-- Category Distribution Chart -->
                    <div class="w3-card w3-round-large w3-white" style="padding: 1.5rem;">
                        <h3 style="color: #1f2937; margin: 0 0 1.5rem 0; font-weight: 600;">
                            <i class="fas fa-chart-pie" style="margin-right: 0.5rem; color: #dc2626;"></i>
                            Distribusi Berdasarkan Kategori Surat
                        </h3>
                        <canvas id="categoryChart" style="max-height: 350px;"></canvas>
                    </div>
                </div>
                
                <!-- Information Panel -->
                <div class="w3-col m4">
                    <!-- Quick Actions -->
                    <div class="w3-card w3-round-large w3-center w3-white" style="margin-bottom: 1.5rem; padding: 2rem;">
                        <h3 style="color: #1f2937; margin: 0 0 2rem 0; font-weight: 600;">
                            <i class="fas fa-bolt" style="margin-right: 0.5rem; color: #dc2626;"></i>
                            Aksi Cepat
                        </h3>
                        
                        <a href="<?= site_url('dashboard/menu_surat_masuk'); ?>" style="text-decoration: none; display: block;">
                            <div class="quick-action-btn" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; padding: 2.5rem; border-radius: 16px; transition: all 0.3s ease; box-shadow: 0 6px 20px rgba(220, 38, 38, 0.3);">
                                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; text-shadow: 0 2px 4px rgba(0,0,0,0.3);"></i>
                                <h2 style="margin: 0; font-weight: 700; font-size: 1.8rem; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">SURAT</h2>
                                <p style="margin: 1rem 0 0 0; opacity: 0.95; font-size: 1rem; font-weight: 500;">Kelola surat masuk & keluar</p>
                            </div>
                        </a>
                        
                        <!-- REKAP SURAT button is hidden as requested -->
                    </div>
                    
                    <!-- Top Categories -->
                    <div class="w3-card w3-round-large w3-white" style="margin-bottom: 1.5rem; padding: 1.5rem;">
                        <h4 style="color: #333; margin: 0 0 1rem 0; font-weight: 600;">
                            <i class="fas fa-star" style="margin-right: 0.5rem; color: #ffc107;"></i>
                            Top Kategori Surat
                        </h4>
                        <div id="topCategories">
                            <div style="text-align: center; color: #999; padding: 2rem;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem;"></i>
                                <p style="margin: 0.5rem 0 0 0;">Memuat data...</p>
                            </div>
                        </div>
                    </div>

                    <!-- System Info -->
                    <div class="w3-card w3-round-large w3-white" style="padding: 1.5rem;">
                        <h4 style="color: #1f2937; margin: 0 0 1rem 0; font-weight: 600;">
                            <i class="fas fa-info-circle" style="margin-right: 0.5rem; color: #f59e0b;"></i>
                            Informasi Sistem
                        </h4>
                        <div style="border-left: 4px solid #28a745; padding-left: 1rem; margin: 1rem 0;">
                            <p style="margin: 0; color: #666; font-size: 0.9rem;">
                                <strong>Status:</strong> <span style="color: #28a745;">● Online</span>
                            </p>
                        </div>
                        <div style="border-left: 4px solid #dc2626; padding-left: 1rem; margin: 1rem 0;">
                            <p style="margin: 0; color: #6b7280; font-size: 0.9rem;">
                                <strong>Pertumbuhan:</strong> <span id="growthPercentage">0%</span>
                            </p>
                        </div>
                        <div style="border-left: 4px solid #ffc107; padding-left: 1rem;">
                            <p style="margin: 0; color: #666; font-size: 0.9rem;">
                                <strong>User:</strong> <?= $this->session->userdata('nama'); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="w3-col m1">
            <div class="w3-container"></div>
        </div>
    </div>
    
    <!-- Modern Footer -->
    <footer style="margin-top: 3rem;">
        <div style="background: #1f2937; color: white; padding: 1.5rem; text-align: center; box-shadow: 0 -2px 10px rgba(0,0,0,0.05); border-top: 2px solid #dc2626;">
            <div style="display: flex; justify-content: center; align-items: center; gap: 1rem; flex-wrap: wrap;">
                <div style="display: flex; align-items: center;">
                                <img src="<?= base_url('assets/img/logo.png') ?>" alt="TRUST Logo" style="height: 25px; width: auto; margin-right: 0.5rem; background: white; border-radius: 4px; padding: 2px;">
            <strong>TRUST</strong> - Tracking Surat Terpadu
                </div>
                <div style="border-left: 1px solid rgba(255,255,255,0.3); padding-left: 1rem;">
                    © 2024 Developed with <i class="fas fa-heart" style="color: #ff6b8a;"></i>
                </div>
            </div>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; opacity: 0.8;">
                Dashboard Analytics - Real-time Letter Tracking
            </div>
        </div>
    </footer>

    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .w3-card {
            animation: fadeInUp 0.6s ease-out;
            animation-fill-mode: both;
        }
        
        .w3-card:nth-child(1) { animation-delay: 0.1s; }
        .w3-card:nth-child(2) { animation-delay: 0.2s; }
        .w3-card:nth-child(3) { animation-delay: 0.3s; }
        .w3-card:nth-child(4) { animation-delay: 0.4s; }
        
        /* Category item styling */
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin: 0.5rem 0;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #dc2626;
            transition: all 0.3s ease;
        }
        
        .category-item:hover {
            background: rgba(220, 38, 38, 0.05);
            transform: translateX(3px);
        }
        
        .category-name {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
        }
        
        .category-count {
            background: #dc2626;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        /* Responsive improvements */
        @media (max-width: 768px) {
            .w3-row-padding {
                margin: 0 !important;
            }
            
            .quick-action-btn h4 {
                font-size: 1rem !important;
            }
            
            footer > div {
                flex-direction: column !important;
                gap: 0.5rem !important;
            }
            
            footer > div > div:last-child {
                border-left: none !important;
                padding-left: 0 !important;
                border-top: 1px solid rgba(255,255,255,0.3);
                padding-top: 0.5rem;
            }
        }
        
        /* Loading animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .fa-spin {
            animation: spin 1s linear infinite;
        }
    </style>
</body>
<script>
// Global chart variables
let monthlyChart, categoryChart;

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadDashboardData();
    
    // Auto refresh every 5 minutes
    setInterval(function() {
        refreshDashboard();
    }, 300000);
    
    // Add hover effects to cards
    const cards = document.querySelectorAll('.w3-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Add click effect to action buttons
    const actionBtns = document.querySelectorAll('.quick-action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
});

// Initialize charts
function initializeCharts() {
    // Monthly trend chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    monthlyChart = new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Surat',
                data: [],
                borderColor: '#dc2626',
                backgroundColor: 'rgba(220, 38, 38, 0.08)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#dc2626',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        }
    });
    
    // Category distribution chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    categoryChart = new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#dc2626',
                    '#f59e0b',
                    '#374151',
                    '#059669',
                    '#ea580c',
                    '#0891b2',
                    '#7c3aed',
                    '#10b981'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// Load dashboard data
function loadDashboardData() {
    // Load chart data
    fetch('<?= site_url("dashboard/chart_data") ?>')
        .then(response => response.json())
        .then(data => {
            updateCharts(data);
        })
        .catch(error => {
            console.error('Error loading chart data:', error);
        });
    
    // Load dashboard stats
    fetch('<?= site_url("dashboard/dashboard_stats") ?>')
        .then(response => response.json())
        .then(data => {
            updateStats(data);
        })
        .catch(error => {
            console.error('Error loading stats:', error);
        });
}

// Update charts with data
function updateCharts(data) {
    // Update monthly chart
    if (data.monthly) {
        const monthLabels = data.monthly.map(item => {
            const date = new Date(item.bulan + '-01');
            return date.toLocaleDateString('id-ID', { month: 'short', year: '2-digit' });
        });
        const monthData = data.monthly.map(item => parseInt(item.jumlah));
        
        monthlyChart.data.labels = monthLabels;
        monthlyChart.data.datasets[0].data = monthData;
        monthlyChart.update();
    }
    
    // Update category chart
    if (data.category) {
        const categoryLabels = data.category.map(item => item.kategori);
        const categoryData = data.category.map(item => parseInt(item.jumlah));
        
        categoryChart.data.labels = categoryLabels;
        categoryChart.data.datasets[0].data = categoryData;
        categoryChart.update();
    }
}

// Update dashboard stats
function updateStats(data) {
    // Update growth percentage
    const growthElement = document.getElementById('growthPercentage');
    if (growthElement && data.growth_percentage !== undefined) {
        const growth = data.growth_percentage;
        const color = growth >= 0 ? '#28a745' : '#dc3545';
        const icon = growth >= 0 ? '↗' : '↘';
        growthElement.innerHTML = `<span style="color: ${color};">${icon} ${Math.abs(growth)}%</span>`;
    }
    
    // Update top categories
    const topCategoriesElement = document.getElementById('topCategories');
    if (topCategoriesElement && data.top_categories) {
        let categoriesHTML = '';
        data.top_categories.forEach(category => {
            if (category.jumlah > 0) {
                categoriesHTML += `
                    <div class="category-item">
                        <span class="category-name">${category.kategori}</span>
                        <span class="category-count">${category.jumlah}</span>
                    </div>
                `;
            }
        });
        
        if (categoriesHTML === '') {
            categoriesHTML = '<div style="text-align: center; color: #999; padding: 1rem;">Belum ada data</div>';
        }
        
        topCategoriesElement.innerHTML = categoriesHTML;
    }
}

// Refresh dashboard
function refreshDashboard() {
    const refreshIcon = document.getElementById('refresh-indicator');
    const lastUpdateElement = document.getElementById('last-update');
    
    if (refreshIcon) {
        refreshIcon.classList.add('fa-spin');
    }
    
    loadDashboardData();
    
    setTimeout(() => {
        if (refreshIcon) {
            refreshIcon.classList.remove('fa-spin');
        }
        if (lastUpdateElement) {
            lastUpdateElement.textContent = `Data diperbarui: ${new Date().toLocaleString('id-ID')}`;
        }
    }, 1000);
}

// Accordion function (keep existing)
function myFunction(id) {
  var x = document.getElementById(id);
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
    x.previousElementSibling.className += " w3-theme-d1";
  } else { 
    x.className = x.className.replace("w3-show", "");
    x.previousElementSibling.className = 
    x.previousElementSibling.className.replace(" w3-theme-d1", "");
  }
}

// Mobile navigation (keep existing)
function openNav() {
  var x = document.getElementById("navDemo");
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
  } else { 
    x.className = x.className.replace(" w3-show", "");
  }
}
</script>
</html>