<?php 
date_default_timezone_set("Asia/Jakarta");


function tglwaktu($date){
    $Bulan = array("<PERSON>","<PERSON>","<PERSON>","<PERSON>",
                    "<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>",
                    "<PERSON><PERSON>","Nov","<PERSON>");
    $Hari = ['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Sa<PERSON><PERSON>'];
    $tahun = substr($date, 0, 4);
    $bulan = substr($date, 5, 2);
    $tgl = substr($date, 8, 2);
    $waktu = substr($date, 11, 5);
    $hari = date("w", strtotime($date));
    return $result = $Hari[$hari].", ".$tgl." ".$Bulan[(int)$bulan-1]." ".$tahun." ".$waktu." WIB";
}

function tgl($date){
    $Bulan = array("<PERSON>","Feb","<PERSON>","Apr",
                    "<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>",
                    "<PERSON><PERSON>","<PERSON>","<PERSON>");
    $Hari = ['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>btu'];
    $tahun = substr($date, 0, 4);
    $bulan = substr($date, 5, 2);
    $tgl = substr($date, 8, 2);
    $waktu = substr($date, 11, 5);
    $hari = date("w", strtotime($date));
    // return $result = $Hari[$hari].", ".$tgl." ".$Bulan[(int)$bulan-1]." ".$tahun;
    return $result = $tgl." ".$Bulan[(int)$bulan-1]." ".$tahun;
}