/* TRUST Theme - Polri Colors (Red-Yellow-Black) */

/* Primary colors - Modern Minimalist */
:root {
    --primary-red: #dc2626;
    --primary-red-dark: #b91c1c;
    --primary-yellow: #f59e0b;
    --primary-yellow-dark: #d97706;
    --primary-black: #1f2937;
    --success-green: #059669;
    --info-blue: #0891b2;
    --warning-orange: #ea580c;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-600: #475569;
    --gray-800: #1e293b;
}

/* Global body styling */
body {
    background: var(--gray-50);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--gray-800);
}

/* Primary buttons */
.btn-primary {
    background: var(--primary-red);
    border: none;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: var(--primary-red-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

/* Secondary buttons */
.btn-secondary {
    background: var(--primary-yellow);
    border: none;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: var(--primary-yellow-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

/* Navigation styling */
.navbar-brand {
    color: white !important;
    font-weight: 600;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

/* Card styling */
.card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.2);
}

.card-header {
    background: var(--primary-black);
    color: white;
    border: none;
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 2px solid var(--primary-red);
}

/* Form controls */
.form-control:focus {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-select:focus {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Table styling */
.table thead th {
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-yellow) 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background: rgba(220, 53, 69, 0.05);
}

/* Alert styling */
.alert-primary {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: var(--primary-red);
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: var(--success-green);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: var(--primary-yellow-dark);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: var(--primary-red);
}

/* Badge styling */
.badge-primary {
    background: var(--primary-red);
}

.badge-secondary {
    background: var(--primary-yellow);
    color: var(--primary-black);
}

/* Custom utilities */
.text-primary {
    color: var(--primary-red) !important;
}

.text-secondary {
    color: var(--primary-yellow-dark) !important;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-red-dark) 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, var(--primary-yellow) 0%, var(--primary-yellow-dark) 100%) !important;
}

/* Progress bars */
.progress-bar {
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-yellow) 100%);
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-yellow) 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-red);
}

/* Loading animations */
@keyframes polri-pulse {
    0%, 100% { 
        background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-yellow) 100%); 
    }
    50% { 
        background: linear-gradient(135deg, var(--primary-yellow) 0%, var(--primary-red) 100%); 
    }
}

.loading-polri {
    animation: polri-pulse 2s ease-in-out infinite;
}

/* Responsive text */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Modern shadows */
.shadow-polri {
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
}

.shadow-polri-lg {
    box-shadow: 0 8px 20px rgba(220, 38, 38, 0.15);
}

/* Icon colors */
.icon-primary {
    color: var(--primary-red);
}

.icon-secondary {
    color: var(--primary-yellow);
}

.icon-success {
    color: var(--success-green);
}

/* Footer styling */
footer {
    background: var(--primary-black);
    color: white;
    border-top: 2px solid var(--primary-red);
}

/* Custom animations */
@keyframes fadeInPolri {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-polri {
    animation: fadeInPolri 0.6s ease-out;
}

/* Additional Modern Utilities */
.text-muted {
    color: var(--gray-600) !important;
}

.border-accent {
    border-left: 4px solid var(--primary-red) !important;
}

.bg-subtle {
    background: var(--gray-100) !important;
}

.hover-lift {
    transition: transform 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-red), var(--primary-yellow));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Clean minimalist borders */
.border-clean {
    border: 1px solid rgba(0, 0, 0, 0.08) !important;
}

/* Soft focus states */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-red) !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.08) !important;
} 