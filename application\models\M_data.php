<?php 

class M_data extends CI_Model{
	function tampil_data($table){
		return $this->db->get($table);
	}
	function tampil_data_dashboard($where,$table){
		$this->db->where($where);
		return $this->db->get($table);
	}
	function tampil_nilai($table){
		$this->db->order_by('kode_soal','asc');
		return $this->db->get($table);
	}
	function input_data($data,$table){
		$this->db->insert($table,$data);
	}
	function proses_data($where,$table){		
		return $this->db->get_where($table,$where);
	}
	function update_data($where,$data,$table){
		$this->db->where($where);
		$this->db->update($table,$data);
	}

	function qr($kodeqr)
{
    if($kodeqr){
        $filename = 'qr/'.$kodeqr;
        if (!file_exists($filename)) { 
                $this->load->library('ciqrcode');
                $params['data'] = $kodeqr;
                $params['level'] = 'H';
                $params['size'] = 10;
                return  $this->ciqrcode->generate($params);
        }
    }
}
}