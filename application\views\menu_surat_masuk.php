<style>
    body {
        background-image: url('<?= base_url('assets/img/log-bg.png') ?>');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        background-repeat: no-repeat;
        min-height: 100vh;
        position: relative;
    }
    
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4);
        z-index: -1;
    }
    
    .menu-card {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .menu-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    }
    
    .input-card {
        background: linear-gradient(135deg, #dc2626, #f59e0b) !important;
    }
    
    .rekap-card {
        background: linear-gradient(135deg, #f59e0b, #dc2626) !important;
    }
    
    .menu-title {
        font-weight: 700 !important;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        color: white !important;
    }
</style>

<div class="w3-row-padding w3-stretch" style="margin-top: 150px">
    <div class="w3-col m2">
        <div class="w3-container"></div>
    </div>
    <div class="w3-col m8">
        <div class="w3-row-padding w3-stretch">
    
            <?php if ($this->session->userdata('id_admin') == 2): ?>
            <div class="w3-col m6">
                <div class="w3-container">
                    <a href="<?= site_url('dashboard/surat_masuk'); ?>" style="text-decoration: none">
                        <div class="w3-card w3-round-large w3-center menu-card input-card">
                            <h1 class="menu-title" style="font-size: 4rem; padding: 40px;">Input<br>Surat</h1>
                        </div>
                    </a>
                </div>
            </div>
            <?php endif; ?>
    
            <div class="w3-col m6">
                <div class="w3-container">
                    <a href="<?= site_url('dashboard/rekap_surat_masuk'); ?>" style="text-decoration: none">
                        <div class="w3-card w3-round-large w3-center menu-card rekap-card">
                            <h1 class="menu-title" style="font-size: 4rem; padding: 40px;">Rekap Data<br>Surat</h1>
                        </div>
                    </a>
                </div>
            </div>
    
        </div>
    </div>
</div>

<footer>
    <div style="background: linear-gradient(135deg, #1f2937, #374151) !important; color: white; position: fixed; left: 0; bottom: 0; width: 100%; text-align: center; padding: 15px; backdrop-filter: blur(10px); border-top: 2px solid #dc2626;">
        <div style="display: flex; justify-content: center; align-items: center; gap: 1rem; flex-wrap: wrap;">
            <img src="<?= base_url('assets/img/logo.png') ?>" alt="TRUST Logo" style="height: 20px; width: auto; background: white; border-radius: 4px; padding: 2px;">
            <label>Copyright 2024 TRUST - Tracking Surat Terpadu | Rumah Sakit Bhayangkara Polda Sumsel</label>
        </div>
    </div>
</footer>

<script>
// Accordion
function myFunction(id) {
  var x = document.getElementById(id);
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
    x.previousElementSibling.className += " w3-theme-d1";
  } else { 
    x.className = x.className.replace("w3-show", "");
    x.previousElementSibling.className = 
    x.previousElementSibling.className.replace(" w3-theme-d1", "");
  }
}

// Used to toggle the menu on smaller screens when clicking on the menu button
function openNav() {
  var x = document.getElementById("navDemo");
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
  } else { 
    x.className = x.className.replace(" w3-show", "");
  }
}
</script>
</html>