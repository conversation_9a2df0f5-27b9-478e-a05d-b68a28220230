<?php
 
class Data_jadwal_shift_pegawai extends CI_Model {
 
    var $table = 'jadwal_shift_pegawai'; //nama tabel dari database
    var $column_order = array(
        'jadwal_shift_pegawai.id_jadwal',
        'data_gaji_pegawai.nama',
        'data_gaji_pegawai.nrk',
        'data_gaji_pegawai.tugas',
        'jadwal_shift_pegawai.tgl_shift_klr',
        'daftar_shift_pegawai.jam_awal',
    ); //field yang ada di table user
    var $column_search = array(
        'jadwal_shift_pegawai.id_jadwal',
        'data_gaji_pegawai.nrk',
        'jadwal_shift_pegawai.tgl_shift_msk',
        'jadwal_shift_pegawai.tgl_shift_klr',
        'data_gaji_pegawai.nama',
        'daftar_shift_pegawai.kode_shift',
        'daftar_shift_pegawai.jam_awal',
        'daftar_shift_pegawai.jam_akhir',
    ); //field yang diizin untuk pencarian 
    var $order = array('jadwal_shift_pegawai.id_shift' => 'asc'); // default order 
 
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }
 
    private function _get_datatables_query()
    {
        $this->db->from($this->table);
        $this->db->join('data_gaji_pegawai','jadwal_shift_pegawai.nrk = data_gaji_pegawai.nrk');
        $this->db->join('daftar_shift_pegawai','daftar_shift_pegawai.id_shift = jadwal_shift_pegawai.id_shift');
        if($this->session->userdata('role') != 'karumkit'){
            $this->db->where('nama_kaur',$this->session->userdata('user_nama'));
        }
        // $this->db->join('jadwal_shift_pegawai','daftar_shift_pegawai.id_shift = jadwal_shift_pegawai.id_shift');
		$i = 0;
	
		foreach ($this->column_search as $item) // loop column 
		{
			if($_POST['search']['value']) // if datatable send POST for search
			{
				
				if($i===0) // first loop
				{
					$this->db->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->db->like($item, $_POST['search']['value']);
				}
				else
				{
					$this->db->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->db->group_end(); //close bracket
			}
			$i++;
		}
         
        if(isset($_POST['order'])) 
        {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } 
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }
 
    function get_datatables()
    {
        $this->_get_datatables_query();
        if($_POST['length'] != -1)
        $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }
 
    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }
 
    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }
 
}