
<?php if($this->session->flashdata('err')){ ?>
    <script>
        Swal.fire({
          icon: "error",
          title: "<?= $this->session->flashdata('err') ?>",
        })
    </script>
<?php } ?>

<?php if($this->session->flashdata('suc')){ ?>
    <script>
        Swal.fire({
          icon: "success",
          title: "<?= $this->session->flashdata('suc') ?>",
        })
    </script>
<?php } ?>

    <div class="w3-row-padding w3-stretch" style="margin-top : 100px">
        <div class="w3-col m1">
            <div class="w3-container"></div>
        </div>
        <div class="w3-col m10">
            <form action="<?= site_url('dashboard/pengaturan_surat_keluar_aksi'); ?>" method="post" enctype="multipart/form-data">
                <div class="w3-container">
                    <div class="w3-container w3-round-large" style="background-color : rgba(186,15,15,0.62) ; color : white; position : relative">
                        <br>
                        <div style="position : absolute; right : 0">
                            <button type="submit" style="background-color : #FFF500; color : red; padding : 20px 70px; font-size : 20px; margin-right : 50px" class="w3-button w3-round-large"><b>SIMPAN</b></button>
                        </div>
                        <h1 class="w3-center"><b>PENGATURAN MULAI NOMOR SURAT KELUAR</b></h1>
                        <br>
                        <div class="w3-row-padding w3-stretch">
                            <div class="w3-col m1">
                                <div class="w3-container"></div>
                            </div>
                            <div class="w3-col m10">
                                <label style="font-size : 18px">No Surat Internal</label>
                                        <input type="number" name="surat_internal" id="noSuratIn" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                <div class="w3-row-padding w3-stretch">
                                    <div class="w3-col m4">
                                        <label style="font-size : 18px">No Surat Biasa</label>
                                        <input type="number" name="surat_biasa" id="noSuratBiasa" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                        <label style="font-size : 18px">No Surat STR</label>
                                        <input type="number" name="surat_str" id="noSuratStr" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                        <label style="font-size : 18px">No Surat SIJ/SIC</label>
                                        <input type="number" name="surat_sij" id="noSuratSij" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                        <label style="font-size : 18px">No Surat MOU</label>
                                        <input type="number" name="surat_mou" id="noSuratBiasa" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                    </div>
                                    <div class="w3-col m4">
                                        <label style="font-size : 18px">No Surat Rahasia</label>
                                        <input type="number" name="surat_rahasia" id="noSuratRahasia" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                        <label style="font-size : 18px">No Surat KEP</label>
                                        <input type="number" name="surat_kep" id="noSuratKep" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                        <label style="font-size : 18px">No Surat SIK</label>
                                        <input type="number" name="surat_sik" id="noSuratSik" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                        <label style="font-size : 18px">No Nota Dinas Satker</label>
                                        <input type="number" name="surat_notadinas" id="noSuratNd" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                    </div>
                                    <div class="w3-col m4">
                                        <label style="font-size : 18px">No Surat Telegram</label>
                                        <input type="number" name="surat_telegram" id="noSuratTele" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                        <label style="font-size : 18px">No Surat Sprin</label>
                                        <input type="number" name="surat_sprin" id="noSuratSprin" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                        <label style="font-size : 18px">No Maklumat</label>
                                        <input type="number" name="surat_maklumat" id="noSuratMaklumat" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                        <label style="font-size : 18px">No Instansi Luar</label>
                                        <input type="number" name="surat_insluar" id="noSuratInluar" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <footer>
        <div style="background-color : red !important; color : white; position: fixed;left: 0;bottom: 0;width: 100%;text-align: center; padding : 10px">
            <label>Copyright 2022 IT Bhayangkara M Hasan Palembang</label>
        </div>
    </footer>
</body>
<script>
// Accordion
function myFunction(id) {
  var x = document.getElementById(id);
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
    x.previousElementSibling.className += " w3-theme-d1";
  } else { 
    x.className = x.className.replace("w3-show", "");
    x.previousElementSibling.className = 
    x.previousElementSibling.className.replace(" w3-theme-d1", "");
  }
}

// Used to toggle the menu on smaller screens when clicking on the menu button
function openNav() {
  var x = document.getElementById("navDemo");
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
  } else { 
    x.className = x.className.replace(" w3-show", "");
  }
}
</script>
<script>
    $(document).ready(function(){
        $.ajax({
            type: 'POST',
            dataType: 'json',
            url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/count_surat',
            dataType: 'json',
            success: function(pengaturan){
                console.log(pengaturan)
                var lan = pengaturan.length;
                for(let i = 0; i < lan; i++){
                    $('input[name="'+pengaturan[i]['kode_jenis']+'"]').val(pengaturan[i]['jumlah_surat']);
                    $('#noSuratIn').val(pengaturan[13]['jumlah_surat'])
                }
            }
        });
        // setInterval(function(){
        //     noUrut() // this will run after every 5 seconds
        // }, 1000);
        
        // $('#jenisSurat').on('change', function(){
        //     var jenis_surat = $('#jenisSurat').val();
        //     $.ajax({
        //         type: 'POST',
        //         dataType: 'json',
        //         data : {jenis_surat : jenis_surat},
        //         url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/count_no_surat',
        //         dataType: 'json',
        //         success: function(noSurat){
        //             $('#noSurat').val(noSurat['no_surat']);
        //         }
        //     });
        //     setInterval(function(){
        //         noSurat() // this will run after every 5 seconds
        //     }, 1000);
        // });
        
    })
    // function noUrut(){
    //     $.ajax({
    //         type: 'POST',
    //         url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/count_no_urut',
    //         dataType: 'json',
    //         success: function(nomor){
    //             $('#noUrut').val(nomor['no_urut']);
    //         }
    //     });
    // }
    
    // function noSurat(){
    //     var jenis_surat = $('#jenisSurat').val();
    //     $.ajax({
    //         type: 'POST',
    //         dataType: 'json',
    //         data : {jenis_surat : jenis_surat},
    //         url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/count_no_surat',
    //         dataType: 'json',
    //         success: function(noSurat){
    //             $('#noSurat').val(noSurat['no_surat']);
    //         }
    //     });
    // }
</script>
</html>