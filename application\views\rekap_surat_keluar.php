
    <?php if($this->session->flashdata('suc')){ ?>
    <script>
        Swal.fire({
          icon: "success",
          title: "<?= $this->session->flashdata('suc') ?>",
        })
    </script>
<?php } ?>
    <div class="w3-row-padding w3-stretch" style="margin-top : 100px">
        <div class="w3-col m12">
            <div class="w3-container">
                <div class="w3-container w3-round-xlarge" style="background-color : white">
                    <br>
                    <a class="w3-round w3-button w3-red" href="<?= site_url('dashboard/surat_keluar'); ?>" style="padding : 20px 60px; font-size : 20px;">Tambah Surat</a>
                    <br>
                    <br>
                    <table id="table" class="ui celled table" style="width:100%">
                        <thead>
                            <tr>
                                <th>No Urut</th>
                                <th><PERSON><PERSON></th>
                                <th>No Surat Keluar</th>
                                <th>Tanggal Surat Keluar</th>
                                <th>Konseptor</th>
                                <th>Kepada</th>
                                <th>Perihal</th>
                                <th>Upload File</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table><br>
                </div>
            </div>
        </div>
    </div>
    <div id="konfirmasi" class="w3-modal">
      <div class="w3-modal-content">
        <div class="w3-container">
          <span onclick="document.getElementById('konfirmasi').style.display='none'"
          class="w3-button w3-display-topright w3-red">&times;</span>
          <br>
          <div class="w3-center">
              <h2>Apakah anda yakin ingin menghapus data berikut?</h2>
                <h2 id="judul"></h2>
          </div>
          <br><br>
            <div class="w3-row-padding w3-stretch">
                <div class="w3-col m3">
                    <div class="w3-container"></div>
                </div>
                <div class="w3-col m3">
                    <div class="w3-container">
                        <a class="w3-button w3-round-xlarge" id="hapusAcc" href="" style="padding : 20px 40px;border : 4px solid green; background-color : green; color : white; font-size : 20px"><b>Hapus</b></a><br>
                    </div>
                </div>
                <div class="w3-col m3">
                    <div class="w3-container">
                        <a class="w3-button w3-round-xlarge" style="padding : 20px 40px; border : 4px solid red; color : red; font-size : 20px" onclick="document.getElementById('konfirmasi').style.display='none'"><b>Batal</b></a><br><br>
                    </div>
                </div>
            </div>
        </div>
      </div>
    </div>
    <div id="edit" class="w3-modal">
      <div class="w3-modal-content" style="margin-top : -70px; width : 100%">
        <div class="w3-container">
          <span id="modalEditClose"
          class="w3-button w3-display-topright w3-red">&times;</span>
          <br>
          <div class="w3-center">
              <h2>Edit Surat</h2>
          </div>
          <br><br>
            <form action="<?= site_url('dashboard/edit_surat_keluar_aksi'); ?>" method="post" enctype="multipart/form-data">
                <div class="w3-container">
                    <div class="w3-container w3-round-large" style="background: linear-gradient(red, transparent); color : white; position : relative">
                        <img src="<?= base_url('/assets/img/polda.png') ?>" style="float : right; width : 100%; max-width : 70px; margin-top : 20px; position : absolute; right : 20px">
                        <h1 class="w3-center"><b>FORM PENGISIAN NOMOR SURAT KELUAR</b></h1>
                        <br><br>
                        <div class="w3-row-padding w3-stretch">
                            <div class="w3-col m4">
                                <div class="w3-container">
                                    <input id="idEdit" name="id_surat_keluar" type="hidden">
                                    <label style="font-size : 18px">No Urut</label>
                                    <input readonly type="text" name="no_urut_sk" id="noUrut" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" value="Loading...." required readonly><br>
                                    <input type="hidden" id="oldJenisSurat">
                                    <label style="font-size : 18px">Jenis Surat</label><br>
                                    <select name="jenis_sk" id="jenisSurat" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px; width : 100%" required>
                                        <option selected disabled value="">--Pilih Jenis Surat--</option>
                                        <?php foreach($jenis as $j) { ?>
                                        <option value="<?= $j->id_jenis; ?>"><?= $j->jenis; ?></option>
                                        <?php } ?>
                                    </select><br><br>
                                    <label style="font-size : 18px">No Surat Keluar</label>
                                    <div class="w3-row ">
                                        <div class="w3-col m2">
                                            <input type="text" id="noSurat1" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" placeholder="B/ND" required><br>
                                        </div>
                                        <div class="w3-col m2">
                                            <div style="display : flex">
                                                <span style="font-size : 2rem; margin-top : 10%">-</span>
                                            <input type="text" id="noSurat2" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" placeholder="" required readonly><br>
                                            </div>
                                        </div>
                                        <div class="w3-col m2">
                                            <div style="display : flex">
                                                <span style="font-size : 2rem; margin-top : 10%">/</span>
                                            <input type="text" id="noSurat3" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" placeholder="" required><br>
                                            </div>
                                        </div>
                                        <div class="w3-col m2">
                                            <div style="display : flex">
                                                <span style="font-size : 2rem; margin-top : 10%">/</span>
                                            <input type="text" id="noSurat4" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" placeholder="" required><br>
                                            </div>
                                        </div>
                                        <div class="w3-col m2">
                                            <div style="display : flex">
                                                <span style="font-size : 2rem; margin-top : 10%">/</span>
                                            <input type="text" id="noSurat5" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" placeholder="" required><br>
                                            </div>
                                        </div>
                                        <div class="w3-col m2">
                                            <div style="display : flex">
                                                <span style="font-size : 2rem; margin-top : 10%">/</span>
                                            <input type="text" id="noSurat6" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" placeholder="" required><br>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" name="no_surat_sk" id="noSuratHidden">
                                    <!--<input type="text" name="no_surat_sk" id="noSurat" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" placeholder="pilh jenis surat terlebih dahulu" required readonly><br>-->
                                    <!--<label style="font-size : 18px">No Agenda</label>-->
                                    <!--<input type="text" name="no_agenda_sk" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>-->
                                    <label style="font-size : 18px">Tanggal Surat Keluar</label>
                                    <input type="date" name="tanggal_surat_sk" class="w3-input w3-round-large" id="tglSurat" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                </div>
                            </div>
                            <div class="w3-col m4">
                                <div class="w3-container">
                                    <label style="font-size : 18px">Konseptor</label>
                                    <input type="text" id="konsep" name="asal_surat_sk" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                    <div id="kepadaSuratEx" style="display : none">
                                        <label style="font-size : 18px">Kepada</label>
                                        <input type="text" name="" id="kepadaSuratExIn" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px;"><br>
                                    </div>
                                    
                                    <div id="kepadaSuratIn" style="display : none">
                                        <label style="font-size : 18px">Kepada</label>
                                        <select multiple="multiple" id="kepadaSurat" name="tes[]"  style="border : 2px solid #BA0F0F; background-color : #e8adad; color : black !important; font-size : 20px; padding : 10px 5px; width : 100%">
                                            <?php foreach($kepada as $k) { ?>
                                            <option value="<?= $k->pejabat; ?>"><?= $k->pejabat; ?></option>
                                            <?php } ?>
                                        </select><br><br>
                                        <input type="hidden" name="" id="inputanMultiKepada">
                                    </div>
                                    <input type="hidden" name="is_internal" id="internalHidden">
                                    <!--<input type="text" name="kepada_sk" id="kepada" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>-->
                                    <label style="font-size : 18px">Perihal</label>
                                    <input type="text" id="perihal" name="perihal_sk" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>
                                    <!--<label style="font-size : 18px">Disposisi KA</label>-->
                                    <!--<input type="text" name="disposisi_ka_sk" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>-->
                                    <!--<label style="font-size : 18px">Tanggal Disposisi</label>-->
                                    <!--<input type="date" name="tanggal_disposisi_sk" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required><br>-->
                                </div>
                            </div>
                            <div class="w3-col m4">
                                <div class="w3-container"> 
                                    <!--<label style="font-size : 18px">Isi Disposisi</label>-->
                                    <!--<textarea name="isi_disposisi_sk" rows="4" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required></textarea><br>-->
                                    <!--<label style="font-size : 18px">Catatan Khusus</label>-->
                                    <!--<textarea name="catatan_sk" id="catatan" rows="4" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px" required></textarea><br>-->
                                    <div id="prevFileLamaDiv">
                                        <br>
                                        <input id="isUbah" name="is_ubah" type="hidden">
                                        <embed src="" id="previewKecilLama" style="width : 100%;" />
                                        <button type="button" id="prevFileLama" class="w3-button w3-round-xlarge" style="border : 2px solid white; color : white">Lihat File Lama</button><br><br>
                                        <p style="color : black">upload ulang file dibawah jika ingin mengubah file lama, jika tidak abaikan saja</p>
                                    </div>
                                    <label style="font-size : 18px">Upload Surat</label>
                                    <input type="file" name="file_sk" id="file_sk" accept=".pdf" class="w3-input w3-round-large" style="border : 2px solid #BA0F0F; background-color : #e8adad; color : #BA0F0F; font-size : 20px; padding : 10px 5px"><br>
                                    <label style="font-size : 18px">Preview Surat</label>
                                    <embed src="" id="previewKecil" style="width : 100%;" />
                                </div>
                            </div>
                        </div>
                        <div class="w3-right">
                            <button type="submit" style="background-color : #FFF500; color : red; padding : 20px 80px; font-size : 20px; margin-right : 50px" class="w3-button w3-round-large"><b>SIMPAN</b></button>
                        </div>
                        <div class="w3-left">
                            <br>
                            <button type="button" class="w3-round-xlarge" style="background-color : rgba(14,6,6,0.35); color : white; border : none; font-size : 1.2rem; padding : 5px 50px; margin-left : 20px"><i>Pastikan data yang diisi adalah sebenar-benarnya</i></button>
                        </div>
                        
                    </div>
                </div>
            </form>
            <br><br>
        </div>
      </div>
    </div>
    <div id="filePrev" class="w3-modal">
      <div class="w3-modal-content" style="margin-top : -30px">
        <div class="w3-container">
          <span onclick="document.getElementById('filePrev').style.display='none'; document.getElementById('judulPrev').innerHTML = 'Berikut File Yang Anda Upload'; document.getElementById('buttonPrev').style.display='none';document.getElementById('previewKecil').src= '';document.getElementById('file_sk').value= null;"
          class="w3-button w3-display-topright w3-red">&times;</span>
            <h3 class="w3-center" id="judulPrev">Berikut File Yang Anda Upload</h3>
            <embed src="" id="preview" style="width : 100%; height : 70vh" />
            <br>
            <div class="w3-center" id="buttonPrev" style="display : none">
                <button class="w3-button w3-round-large w3-green" style="font-size : 1.2rem" onclick="document.getElementById('filePrev').style.display='none'; document.getElementById('judulPrev').innerHTML = 'Berikut File Yang Anda Upload'; document.getElementById('buttonPrev').style.display='none';">Simpan</button><br><br>
            </div>
        </div>
      </div>
    </div>
    <footer>
        <div style="background-color : red !important; color : white; position: fixed;left: 0;bottom: 0;width: 100%;text-align: center; padding : 10px">
            <label>Copyright 2022 IT Bhayangkara M Hasan Palembang</label>
        </div>
    </footer>
</body>
<script>
// Accordion
function myFunction(id) {
  var x = document.getElementById(id);
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
    x.previousElementSibling.className += " w3-theme-d1";
  } else { 
    x.className = x.className.replace("w3-show", "");
    x.previousElementSibling.className = 
    x.previousElementSibling.className.replace(" w3-theme-d1", "");
  }
}

// Used to toggle the menu on smaller screens when clicking on the menu button
function openNav() {
  var x = document.getElementById("navDemo");
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
  } else { 
    x.className = x.className.replace(" w3-show", "");
  }
}

function prev(files,namaFile){
    $('#filePrev').show();
    $('#preview').attr("src","<?php echo base_url('lampiran/')?>"+files);
    $('#judulPrev').html(namaFile);
}

function hapus(link,judul,file){
    $('#konfirmasi').show();
    $('#judul').html(judul);
    $('#hapusAcc').attr("href","<?php echo site_url('dashboard/hapus_sk/')?>"+link+'/'+file);
}

function edit(id,noUrut,jenis,noSurat,tglSurat,konsep,kepada,perihal,namaFile){
    $('#edit').show();
    $('#idEdit').val(id);
    $('#noUrut').val(noUrut);
    $('#jenisSurat').val(jenis).change();
    $('#oldJenisSurat').val(jenis);
    $('#noSurat').val(noSurat);
    $('#tglSurat').val(tglSurat);
    $('#konsep').val(konsep);
    console.log(jenis)
    if(jenis == '13'){
        $('#inputanMultiKepada').val(kepada);
        var splitIn = kepada;
        var result = splitIn.split(', ');
        $("#kepadaSurat").val(result).change();
        console.log(jenis)
    }
    else{
        $('#kepadaSuratExIn').val(kepada);
    }
    $('#perihal').val(perihal);
    // $('#catatan').val(catatan);
    $('#previewKecilLama').attr("src","<?php echo base_url('lampiran/')?>"+namaFile);
    $('#prevFileLama').attr('onclick', 'prev("'+namaFile+'","'+namaFile+'")');
    $('#prevFileLamaDiv').show();
    $('#isUbah').val('0');
    throwNoSurat(noUrut)
}
</script>
<script>
    $(document).ready( function () {
        var change = 0;
        $('#jenisSurat').select2({
            theme: "classic"
        });
        $('#kepadaSurat').select2({
            theme: "classic"
        });
        $('#jenisSurat').on('change', function(){
                var jenis_surat = $('#jenisSurat').val();
                var old_jenis_surat = $('#oldJenisSurat').val();
                var noUrutIdentification = $('#noUrut').val();
                if(jenis_surat != old_jenis_surat){
                    $.ajax({
                        type: 'POST',
                        dataType: 'json',
                        data : {jenis_surat : jenis_surat},
                        url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/count_no_surat',
                        dataType: 'json',
                        success: function(noSurat){
                            $('#noSurat2').val(noSurat['no_surat']);
                        }
                    });
                    setInterval(function(){
                        noSurat() // this will run after every 5 seconds
                        noSuratHidden()
                    }, 1000);
                    if(jenis_surat == '13'){
                        $('#kepadaSuratEx').hide();
                        $('#kepadaSuratExIn').attr('name', '');
                        $('#kepadaSuratIn').show();
                        $('#inputanMultiKepada').attr('name', 'kepada_sk');
                        $('#internalHidden').val('1');
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/bln_thn',
                            dataType: 'json',
                            success: function(blmThn){
                                $('#noSurat3').val(blmThn['bulan']);
                                $('#noSurat5').val(blmThn['tahun']);
                                $('#noSurat6').val(blmThn['user']);
                            }
                        });
                    }
                    else{
                        $('#kepadaSuratEx').show();
                        $('#kepadaSuratExIn').attr('name', 'kepada_sk');
                        $('#kepadaSuratIn').hide();
                        $('#inputanMultiKepada').attr('name', '');
                        $('#noSurat6').val('Rumkit');
                        $('#internalHidden').val('0');
                        $('#noSurat6').val('Rumkit');
                        $('#internalHidden').val('0');
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/bln_thn',
                            dataType: 'json',
                            success: function(blmThn){
                                $('#noSurat3').val(blmThn['bulan']);
                                $('#noSurat5').val(blmThn['tahun']);
                            }
                        });
                    }
                }
                else{
                    throwNoSurat(noUrutIdentification)
                    setInterval(function(){
                        noSurat() // this will run after every 5 seconds
                        noSuratHidden()
                    }, 1000);
                    if(jenis_surat == '13'){
                        $('#kepadaSuratEx').hide();
                        $('#kepadaSuratExIn').attr('name', '');
                        $('#kepadaSuratIn').show();
                        $('#inputanMultiKepada').attr('name', 'kepada_sk');
                        $('#internalHidden').val('1');
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/bln_thn',
                            dataType: 'json',
                            success: function(blmThn){
                                $('#noSurat3').val(blmThn['bulan']);
                                $('#noSurat5').val(blmThn['tahun']);
                                $('#noSurat6').val(blmThn['user']);
                            }
                        });
                    }
                    else{
                        $('#kepadaSuratEx').show();
                        $('#kepadaSuratExIn').attr('name', 'kepada_sk');
                        $('#kepadaSuratIn').hide();
                        $('#inputanMultiKepada').attr('name', '');
                        $('#noSurat6').val('Rumkit');
                        $('#internalHidden').val('0');
                        $('#noSurat6').val('Rumkit');
                        $('#internalHidden').val('0');
                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/bln_thn',
                            dataType: 'json',
                            success: function(blmThn){
                                $('#noSurat3').val(blmThn['bulan']);
                                $('#noSurat5').val(blmThn['tahun']);
                            }
                        });
                    }
                }
        });
        $('#table').DataTable({
            dom: 'Bfrtip',
            "processing": true, 
            "serverSide": true, 
            "order": [], 
             
            "ajax": {
                "url": "<?php echo site_url('dashboard/data_surat_keluar')?>",
                "type": "POST"
            },
                  lengthMenu: [
                      [10, 25, 50, -1],
                      ['10 rows', '25 rows', '50 rows', 'Show all']
                  ],
                    buttons: [
                        {
                          extend: 'excelHtml5',
                          title: 'Data Absensi Tanggal ',
                      },'pageLength'
                    ],
        });
        $("#file_sk").on('change', function () {
                    const filex = this.files[0];
                    if (filex) {
                        let reader = new FileReader();
                        reader.onload = function (event) {
                            $("#preview")
                              .attr("src", event.target.result);
                              $("#previewKecil")
                              .attr("src", event.target.result);
                        };
                        reader.readAsDataURL(filex);
                        
                    }
                    $('#buttonPrev').show();
                    $('#prevFileLamaDiv').hide();
                    $('#filePrev').show();
                    $('#isUbah').val('1');
                });
        $('#kepadaSurat').on('change', function (e) {
            var tes = "";
            e.preventDefault();

            //Get Text
            var selected = $('#kepadaSurat').select2("data");
            for (var i = 0; i <= selected.length-1; i++) {
                var tes = tes + selected[i].text + ', ';
            }
            console.log(tes);
            $('#inputanMultiKepada').val(tes)
        });
        $('#modalEditClose').on('click', function(){
            change = 0;
            console.log(change)
            $('#edit').hide();
            $('#file_sk').val(null);
            $('#previewKecil').attr('src','')
        //   document.getElementById('edit').style.display='none';
        //   document.getElementById('file_sk').value= null;
        //   document.getElementById('previewKecil').src= ''; 
        });
    } );
    
    function noSurat(){
        var jenis_surat = $('#jenisSurat').val();
        $.ajax({
            type: 'POST',
            dataType: 'json',
            data : {jenis_surat : jenis_surat},
            url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/count_no_surat',
            dataType: 'json',
            success: function(noSurat){
                $('#noSurat').val(noSurat['no_surat']);
            }
        });
    }
    var a,b,c,d,e,f = "";
    function noSuratHidden(){
        var a = $('#noSurat1').val();
        var b = $('#noSurat2').val();
        var c = $('#noSurat3').val();
        var d = $('#noSurat4').val();
        var e = $('#noSurat5').val();
        var f = $('#noSurat6').val();
        $('#noSuratHidden').val(a + '-' + b + '/' + c + '/' + d + '/' + e + '/' + f)
    }
    function throwNoSurat(nomor_urut){
        $.ajax({
                type: 'POST',
                dataType: 'json',
                data : {nomor_urut : nomor_urut},
                url: 'https://penomoran.rsbhayangkarapalembang.id/index.php/dashboard/edit_surat_api',
                dataType: 'json',
                success: function(no_surat){
                    $('#noSurat1').val(no_surat['no_surat_1']);
                    $('#noSurat2').val(no_surat['no_surat_2']);
                    $('#noSurat3').val(no_surat['no_surat_3']);
                    $('#noSurat4').val(no_surat['no_surat_4']);
                    $('#noSurat5').val(no_surat['no_surat_5']);
                    $('#noSurat6').val(no_surat['no_surat_6']);
                }
            });
    }
</script>
</html>