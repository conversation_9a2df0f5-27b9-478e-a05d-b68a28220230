<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {

	public function __construct()
    {
        parent::__construct();
        $this->load->model('data_surat_masuk');
        if($this->session->userdata('role') == '')
        {
            redirect('login');
        }
        $this->output->disable_cache();
    }

    public function index()
    {
      $data['total_sm'] = $this->db->query("SELECT * FROM surat_masuk")->num_rows();
      $data['surat_bulan_ini'] = $this->db->query("SELECT * FROM surat_masuk WHERE MONTH(waktu_sm) = MONTH(CURDATE()) AND YEAR(waktu_sm) = YEAR(CURDATE())")->num_rows();
      $data['surat_minggu_ini'] = $this->db->query("SELECT * FROM surat_masuk WHERE YEARWEEK(waktu_sm) = YEARWEEK(CURDATE())")->num_rows();
      $data['surat_hari_ini'] = $this->db->query("SELECT * FROM surat_masuk WHERE DATE(waktu_sm) = CURDATE()")->num_rows();
      
      $this->load->view('template/header');
      $this->load->view('dashboard',$data);
    }
    
    function surat_masuk()
    {
        $data['jenis'] = $this->db->query("SELECT * FROM jenis_surat")->result();
        $data['kepada'] = $this->db->query("SELECT * FROM kepada_internal")->result();
        $data['disposisi'] = $this->db->query("SELECT * FROM jenis_disposisi")->result();
        $this->load->view('template/header');
        $this->load->view("surat_masuk",$data);
    }
    
    function menu_surat_masuk()
    {
        $this->load->view('template/header');
        $this->load->view("menu_surat_masuk");
    }
    
    function rekap_surat_masuk()
    {
        $data['jenis'] = $this->db->query("SELECT * FROM jenis_surat")->result();
        $data['kepada'] = $this->db->query("SELECT * FROM kepada_internal")->result();
        $data['disposisi'] = $this->db->query("SELECT * FROM jenis_disposisi")->result();
        $this->load->view('template/header');
        $this->load->view("rekap_surat_masuk",$data);
    }
    
    function surat_masuk_aksi()
    {
        $no_urut_sm = $this->input->post('no_urut_sm');
        $jenis_sm = $this->input->post('jenis_sm');
        $no_agenda_sm = $this->input->post('no_agenda_sm');
        $tanggal_surat_sm = $this->input->post('tanggal_surat_sm');
        $no_surat_sm = $this->input->post('no_surat_sm');
        $asal_surat_sm = $this->input->post('asal_surat_sm');
        $kepada_sm = substr($this->input->post('kepada_sm'),0,-2);
        $perihal_sm = $this->input->post('perihal_sm');
        $disposisi_ka_sm = substr($this->input->post('disposisi_ka_sm'),0,-2);
        $isi_disposisi_sm = substr($this->input->post('isi_disposisi_sm'),0,-2);
        if($isi_disposisi_sm == '0')
        {
            $isi_d_sm = '-';
        }
        else
        {
            $isi_d_sm = substr($this->input->post('isi_disposisi_sm'),0,-2);
        }
        if($disposisi_ka_sm == '0')
        {
            $d_ka_sm = '-';
        }
        else
        {
            $d_ka_sm = substr($this->input->post('disposisi_ka_sm'),0,-2);
        }
        $catatan_sm = $this->input->post('catatan_sm');
        $file_sm = $_FILES['file_sm']['name'];
        if($file_sm != NULL)
        {
            $nama_file = $no_urut_sm.'-'.$jenis_sm.'-'.preg_replace('/\s+/', '_', $perihal_sm).'.'.pathinfo($file_sm, PATHINFO_EXTENSION);
            $conf_file = array(
                    'upload_path' => "./lampiran_masuk",
                    'allowed_types' => "*",
                    'max_size' => '100000',
                    'file_name' => $nama_file,
                );
            $this->load->library('upload', $conf_file);
            $this->upload->initialize($conf_file);
            $this->upload->do_upload('file_sm');
        }
        else
        {
            $nama_file = '-';
        }
        $input = array(
                'no_urut_sm' => $no_urut_sm,
                'jenis_sm' => $jenis_sm,
                'no_agenda_sm' => $no_agenda_sm,
                'tanggal_surat_sm' => $tanggal_surat_sm,
                'no_surat_sm' => $no_surat_sm,
                'asal_surat_sm' => $asal_surat_sm,
                'kepada_sm' => $kepada_sm,
                'perihal_sm' => $perihal_sm,
                'disposisi_ka_sm' => $d_ka_sm,
                'isi_disposisi_sm' => $isi_d_sm,
                'catatan_sm' => $catatan_sm,
                'file_sm' => $nama_file,
                'id_admin' => $this->session->userdata('id_admin'),
            );
        if($this->db->insert('surat_masuk',$input) == '1')
        {
            $this->session->set_flashdata('suc','berhasil!');
            redirect('dashboard/surat_masuk');
        }
        else
        {
            $this->session->set_flashdata('err','error, silahkan coba lagi!');
            redirect('dashboard/surat_masuk');
        }
    }
    
    function edit_surat_masuk_aksi()
    {
        $id_surat_masuk = $this->input->post('id_surat_masuk');
        $no_urut_sm = $this->input->post('no_urut_sm');
        $jenis_sm = $this->input->post('jenis_sm');
        $tanggal_surat_sm = $this->input->post('tanggal_surat_sm');
        $no_agenda_sm = $this->input->post('no_agenda_sm');
        $no_surat_sm = $this->input->post('no_surat_sm');
        $asal_surat_sm = $this->input->post('asal_surat_sm');
        $kepada_sm = substr($this->input->post('kepada_sm'),0,-2);
        $perihal_sm = $this->input->post('perihal_sm');
        $disposisi_ka_sm = substr($this->input->post('disposisi_ka_sm'),0,-2);
        $isi_disposisi_sm = substr($this->input->post('isi_disposisi_sm'),0,-2);
        if($isi_disposisi_sm == '0')
        {
            $isi_d_sm = '-';
        }
        else
        {
            $isi_d_sm = substr($this->input->post('isi_disposisi_sm'),0,-2);
        }
        if($disposisi_ka_sm == '0')
        {
            $d_ka_sm = '-';
        }
        else
        {
            $d_ka_sm = substr($this->input->post('disposisi_ka_sm'),0,-2);
        }
        $catatan_sm = $this->input->post('catatan_sm');

        $is_ubah = $this->input->post('is_ubah');
        
        if($is_ubah == '1')
        {
            $file = $this->db->query("SELECT file_sm FROM surat_masuk WHERE id_surat_masuk = '$id_surat_masuk' ")->row()->file_sm;
            $path = "lampiran_masuk/";
            $names = array(
                    $file,
                );
            foreach ($names as $name) {
              foreach (glob($path . $name . '*') as $filename) {
                unlink(realpath($filename));
              }
            }
            
            $file_sm = $_FILES['file_sm']['name'];
            $nama_file = $no_urut_sm.'-'.$jenis_sm.'-'.preg_replace('/\s+/', '_', $perihal_sm).'.'.pathinfo($file_sm, PATHINFO_EXTENSION);
            if($file_sm != NULL)
            {
                $conf_file = array(
                        'upload_path' => "./lampiran_masuk",
                        'allowed_types' => "*",
                        'max_size' => '100000',
                        'file_name' => $nama_file,
                    );
                $this->load->library('upload', $conf_file);
                $this->upload->initialize($conf_file);
                $this->upload->do_upload('file_sm');
                
                $edit = array(
                    'no_urut_sm' => $no_urut_sm,
                    'jenis_sm' => $jenis_sm,
                    'no_agenda_sm' => $no_agenda_sm,
                    'tanggal_surat_sm' => $tanggal_surat_sm,
                    'no_surat_sm' => $no_surat_sm,
                    'asal_surat_sm' => $asal_surat_sm,
                    'kepada_sm' => $kepada_sm,
                    'perihal_sm' => $perihal_sm,
                    'disposisi_ka_sm' => $d_ka_sm,
                    'isi_disposisi_sm' => $isi_d_sm,
                    'catatan_sm' => $catatan_sm,
                    'file_sm' => $nama_file,
                );
                $this->db->update('surat_masuk',$edit,array('id_surat_masuk' => $id_surat_masuk));
            }
            
        }
        else
        {
            $edit = array(
                    'no_urut_sm' => $no_urut_sm,
                    'jenis_sm' => $jenis_sm,
                    'no_agenda_sm' => $no_agenda_sm,
                    'tanggal_surat_sm' => $tanggal_surat_sm,
                    'no_surat_sm' => $no_surat_sm,
                    'asal_surat_sm' => $asal_surat_sm,
                    'kepada_sm' => $kepada_sm,
                    'perihal_sm' => $perihal_sm,
                    'disposisi_ka_sm' => $d_ka_sm,
                    'isi_disposisi_sm' => $isi_d_sm,
                    'catatan_sm' => $catatan_sm,
                );
            $this->db->update('surat_masuk',$edit,array('id_surat_masuk' => $id_surat_masuk));
        }
        $this->session->set_flashdata('suc','berhasil!');
        redirect('dashboard/rekap_surat_masuk');
    }
    
    function hapus_sm($id,$file)
    {
        $this->db->query("DELETE FROM surat_masuk WHERE id_surat_masuk = '$id' ");
        $path = "lampiran_masuk/";
        $names = array(
                $file,
            );
        foreach ($names as $name) {
          foreach (glob($path . $name . '*') as $filename) {
            unlink(realpath($filename));
          }
        }
        $this->session->set_flashdata('suc','berhasil!');
        redirect('dashboard/rekap_surat_masuk');
    }
    
    function count_no_urut_sm()
    {
        $output = $this->db->query('SELECT COUNT(no_urut_sm) + 1 AS no_urut FROM surat_masuk')->row();
        echo json_encode($output);
    }
    
    function bln_thn()
    {
        $bln = $this->input->post('bln');
        $thn = $this->input->post('thn');
        $data = $this->data_surat_masuk->datatables_filter_bulan($bln,$thn);
        echo json_encode($data);
    }
    
    function data_surat_masuk()
    {
        $list = $this->data_surat_masuk->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $field) {
            $no++;
            $row = array();
            $row[] = $no;
            $row[] = $field->no_urut_sm;
            $row[] = $field->jenis;
            $row[] = $field->no_agenda_sm;
            $row[] = $field->tanggal_surat_sm;
            $row[] = $field->no_surat_sm;
            $row[] = $field->asal_surat_sm;
            $row[] = $field->kepada_sm;
            $row[] = $field->perihal_sm;
            $row[] = $field->disposisi_ka_sm;
            $row[] = $field->isi_disposisi_sm;
            $row[] = $field->catatan_sm;
            if($field->file_sm == '-'){
                $row[] = '-';
            }
            else{
                $row[] = '<a href="'.base_url().'/lampiran_masuk/'.$field->file_sm.'" target="_blank" style="text-decoration : none"><i class="fa fa-file-pdf-o"></i> Lihat File</a>';
            }
            $row[] = '<button class="mini ui button green" onclick="edit_surat_masuk('.$field->id_surat_masuk.')"><i class="edit icon"></i></button> <a href="'.site_url('dashboard/hapus_sm/'.$field->id_surat_masuk.'/'.$field->file_sm).'" class="mini ui button red" onclick="return confirm(\'Yakin?\')"><i class="trash icon"></i></a>';
            $data[] = $row;
        }
        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->data_surat_masuk->count_all(),
            "recordsFiltered" => $this->data_surat_masuk->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }
    
    function edit_surat_api()
    {
        $id = $this->input->post('id');
        $query = $this->db->query("SELECT * FROM surat_masuk WHERE id_surat_masuk = '$id' ")->row();
        $data = array(
            'no_urut_sm' => $query->no_urut_sm,
            'jenis_sm' => $query->jenis_sm,
            'no_agenda_sm' => $query->no_agenda_sm,
            'tanggal_surat_sm' => $query->tanggal_surat_sm,
            'no_surat_sm' => $query->no_surat_sm,
            'asal_surat_sm' => $query->asal_surat_sm,
            'kepada_sm' => $query->kepada_sm,
            'perihal_sm' => $query->perihal_sm,
            'disposisi_ka_sm' => $query->disposisi_ka_sm,
            'isi_disposisi_sm' => $query->isi_disposisi_sm,
            'catatan_sm' => $query->catatan_sm,
            'file_sm' => $query->file_sm,
            'id_surat_masuk' => $query->id_surat_masuk,
        );
        echo json_encode($data);
    }
    
    function romawi($n) {
        $result = '';
        $lookup = array('M' => 1000,'CM' => 900,'D' => 500,'CD' => 400,'C' => 100,'XC' => 90,'L' => 50,'XL' => 40,'X' => 10,'IX' => 9,'V' => 5,'IV' => 4,'I' => 1);
        foreach($lookup as $roman => $value){
            $matches = intval($n/$value);
            $result .= str_repeat($roman,$matches);
            $n = $n % $value;
        }
        return $result;
    }

    public function chart_data()
    {
        $monthly_data = $this->db->query("
            SELECT 
                DATE_FORMAT(waktu_sm, '%Y-%m') as bulan,
                COUNT(*) as jumlah 
            FROM surat_masuk 
            WHERE waktu_sm >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(waktu_sm, '%Y-%m')
            ORDER BY bulan ASC
        ")->result();

        $category_data = $this->db->query("
            SELECT 
                js.jenis as kategori,
                COUNT(sm.id_surat_masuk) as jumlah
            FROM jenis_surat js
            LEFT JOIN surat_masuk sm ON js.id_jenis = sm.jenis_sm
            GROUP BY js.id_jenis, js.jenis
            ORDER BY jumlah DESC
        ")->result();

        $weekly_data = $this->db->query("
            SELECT 
                WEEK(waktu_sm) as minggu,
                COUNT(*) as jumlah 
            FROM surat_masuk 
            WHERE waktu_sm >= DATE_SUB(CURDATE(), INTERVAL 4 WEEK)
            GROUP BY WEEK(waktu_sm)
            ORDER BY minggu ASC
        ")->result();

        $response = array(
            'monthly' => $monthly_data,
            'category' => $category_data,
            'weekly' => $weekly_data
        );

        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function dashboard_stats()
    {
        $stats = array();
        
        $stats['total_surat'] = $this->db->query("SELECT COUNT(*) as total FROM surat_masuk")->row()->total;
        
        $stats['bulan_ini'] = $this->db->query("
            SELECT COUNT(*) as total 
            FROM surat_masuk 
            WHERE MONTH(waktu_sm) = MONTH(CURDATE()) AND YEAR(waktu_sm) = YEAR(CURDATE())
        ")->row()->total;
        
        $stats['minggu_ini'] = $this->db->query("
            SELECT COUNT(*) as total 
            FROM surat_masuk 
            WHERE YEARWEEK(waktu_sm) = YEARWEEK(CURDATE())
        ")->row()->total;
        
        $stats['hari_ini'] = $this->db->query("
            SELECT COUNT(*) as total 
            FROM surat_masuk 
            WHERE DATE(waktu_sm) = CURDATE()
        ")->row()->total;

        $stats['top_categories'] = $this->db->query("
            SELECT 
                js.jenis as kategori,
                COUNT(sm.id_surat_masuk) as jumlah
            FROM jenis_surat js
            LEFT JOIN surat_masuk sm ON js.id_jenis = sm.jenis_sm
            GROUP BY js.id_jenis, js.jenis
            ORDER BY jumlah DESC
            LIMIT 5
        ")->result();

        $stats['top_disposisi'] = $this->db->query("
            SELECT 
                disposisi_ka_sm as disposisi,
                COUNT(*) as jumlah
            FROM surat_masuk 
            WHERE disposisi_ka_sm != '-' AND disposisi_ka_sm != ''
            GROUP BY disposisi_ka_sm
            ORDER BY jumlah DESC
            LIMIT 5
        ")->result();

        $bulan_lalu = $this->db->query("
            SELECT COUNT(*) as total 
            FROM surat_masuk 
            WHERE MONTH(waktu_sm) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)) 
            AND YEAR(waktu_sm) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
        ")->row()->total;

        if($bulan_lalu > 0) {
            $stats['growth_percentage'] = round((($stats['bulan_ini'] - $bulan_lalu) / $bulan_lalu) * 100, 1);
        } else {
            $stats['growth_percentage'] = 0;
        }

        header('Content-Type: application/json');
        echo json_encode($stats);
    }
}
