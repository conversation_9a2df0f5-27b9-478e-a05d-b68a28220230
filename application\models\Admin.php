<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Admin extends CI_Model
{
    //fungsi cek session logged in
    function is_logged_in()
    {
        return $this->session->userdata('user_id');
    }

    //fungsi cek level
    function is_role()
    {
        return $this->session->userdata('role');
    }

    //fungsi check login
    function check_login($table, $field1,$field2)
    {
        //$this->db->select('admin.id,admin.user,admin.pass,admin.role,admin.nama,admin.jabatan_surat');
        //$this->db->from($table);
        //$this->db->where($field1);
        //$this->db->limit(1);
        $query = $this->db->query("SELECT * FROM $table WHERE username LIKE BINARY '$field1' AND password LIKE BINARY '$field2' LIMIT 1");
        if ($query->num_rows() == 0) {
            return FALSE;
        } else {
            return $query->result();
        }
    }
}