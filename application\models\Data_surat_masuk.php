<?php
 
class Data_surat_masuk extends CI_Model {
 
    var $table = 'surat_masuk'; //nama tabel dari database
    var $column_order = array(
        'no_urut_sm',
        'jenis',
        'no_agenda_sm',
        'tanggal_surat_sm',
        'no_surat_sm',
        'asal_surat_sm',
        'kepada_sm',
        'perihal_sm',
        'disposisi_ka_sm',
        'isi_disposisi_sm',
        'catatan_sm',
        'file_sm',
        'id_surat_masuk'
    ); //field yang ada di table user
    var $column_search = array(
        'no_urut_sm',
        'jenis',
        'no_agenda_sm',
        'tanggal_surat_sm',
        'no_surat_sm',
        'asal_surat_sm',
        'kepada_sm',
        'perihal_sm',
        'disposisi_ka_sm',
        'isi_disposisi_sm',
        'catatan_sm',
        'file_sm',
        'id_surat_masuk'
    ); //field yang diizin untuk pencarian 
    var $order = array('id_surat_masuk' => 'desc'); // default order 
 
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }
 
    private function _get_datatables_query()
    {

       $this->db->from($this->table);
       $this->db->join('jenis_surat','surat_masuk.jenis_sm = jenis_surat.id_jenis');
       //$this->db->where('id_admin',$this->session->userdata('id_admin'));
		$i = 0;
	
		foreach ($this->column_search as $item) // loop column 
		{
			if($_POST['search']['value']) // if datatable send POST for search
			{
				
				if($i===0) // first loop
				{
					$this->db->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->db->like($item, $_POST['search']['value']);
				}
				else
				{
					$this->db->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->db->group_end(); //close bracket
			}
			$i++;
		}
         
        if(isset($_POST['order'])) 
        {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } 
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }
 
    function get_datatables()
    {
        $this->_get_datatables_query();
        if($_POST['length'] != -1)
        $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }
 
    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }
 
    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }
 
}