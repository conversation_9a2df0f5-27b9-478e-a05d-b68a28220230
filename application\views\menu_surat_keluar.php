
    
    <div class="w3-row-padding w3-stretch" style="margin-top : 150px">
        <div class="w3-col m2">
            <div class="w3-container"></div>
        </div>
        <div class="w3-col m8">
            <div class="w3-row-padding w3-stretch">
                <div class="w3-col m6">
                    <div class="w3-container">
                        <a href="<?= site_url('dashboard/surat_keluar'); ?>" style="text-decoration : none">
                            <div class="w3-card w3-round-large w3-center" style="background-color : rgba(250,121,121,0.68); color : white">
                                <h1 style="font-size : 4rem; padding : 40px">Input<br>Surat Keluar</h1>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="w3-col m6">
                    <div class="w3-container">
                        <a href="<?= site_url('dashboard/rekap_surat_keluar'); ?>" style="text-decoration : none">
                            <div class="w3-card w3-round-large w3-center" style="background-color : rgba(186,15,15,0.68); color : white">
                                <h1 style="font-size : 4rem; padding : 40px">Rekap Data<br>Surat Keluar</h1>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer>
        <div style="background-color : red !important; color : white; position: fixed;left: 0;bottom: 0;width: 100%;text-align: center; padding : 10px">
            <label>Copyright 2022 IT Bhayangkara M Hasan Palembang</label>
        </div>
    </footer>
</body>
<script>
// Accordion
function myFunction(id) {
  var x = document.getElementById(id);
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
    x.previousElementSibling.className += " w3-theme-d1";
  } else { 
    x.className = x.className.replace("w3-show", "");
    x.previousElementSibling.className = 
    x.previousElementSibling.className.replace(" w3-theme-d1", "");
  }
}

// Used to toggle the menu on smaller screens when clicking on the menu button
function openNav() {
  var x = document.getElementById("navDemo");
  if (x.className.indexOf("w3-show") == -1) {
    x.className += " w3-show";
  } else { 
    x.className = x.className.replace(" w3-show", "");
  }
}
</script>
</html>