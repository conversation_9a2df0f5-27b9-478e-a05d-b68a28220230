<?php
 
class Data_pegawai_update extends CI_Model {
 
    var $table = 'data_gaji_pegawai'; //nama tabel dari database
    var $column_order = array(
        'id_gaji',
        'email',
        'nama',
        'nik',
        'nrk',
        'tmpt_lahir',
        'pendidikan',
        'no_ijazah',
        'no_str',
        'tmt_str',
        'masa_kontrak',
        'tmt',
        'tugas',
        'gaji',
    ); //field yang ada di table user
    var $column_search = array(
        'id_gaji',
        'email',
        'nama',
        'nik',
        'nrk',
        'tmpt_lahir',
        'pendidikan',
        'no_ijazah',
        'no_str',
        'tmt_str',
        'masa_kontrak',
        'tmt',
        'tugas',
        'gaji',
    ); //field yang diizin untuk pencarian 
    var $order = array('id_gaji' => 'asc'); // default order 
 
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }
 
    private function _get_datatables_query()
    {
        $this->db->from($this->table);
        $minus = "-";
        $gaada = NULL;
        //$this->db->where('nrk',$this->session->userdata('nrk'));
        $this->db->where("nik IS NOT NULL");
        // $this->db->where("nik !=",NULL);
        // $this->db->where("foto !=","-");
        // $this->db->where("foto !=",NULL);
		$i = 0;
	
		foreach ($this->column_search as $item) // loop column 
		{
			if($_POST['search']['value']) // if datatable send POST for search
			{
				
				if($i===0) // first loop
				{
					$this->db->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->db->like($item, $_POST['search']['value']);
				}
				else
				{
					$this->db->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->db->group_end(); //close bracket
			}
			$i++;
		}
         
        if(isset($_POST['order'])) 
        {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } 
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }
 
    function get_datatables()
    {
        $this->_get_datatables_query();
        if($_POST['length'] != -1)
        $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }
 
    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }
 
    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }
 
}