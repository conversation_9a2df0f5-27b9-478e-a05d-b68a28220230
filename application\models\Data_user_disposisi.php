<?php
 
class Data_user_disposisi extends CI_Model {
 
    var $table = 'surat_masuk'; //nama tabel dari database
    var $column_order = array(
        'id_surat_masuk',
        'pembuat_surat',
        'no_surat',
        'tanggal',
        'pengirim',
        'derajat',
        'perihal',
        'klasifikasi',
        'jenis',
        'kunci',
        'isi',
    ); //field yang ada di table user
    var $column_search = array(
        'id_surat_masuk',
        'pembuat_surat',
        'no_surat',
        'tanggal',
        'pengirim',
        'derajat',
        'perihal',
        'klasifikasi',
        'jenis',
        'kunci',
        'isi',
    ); //field yang diizin untuk pencarian 
    var $order = array('id_surat_masuk' => 'desc'); // default order 
 
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }
 
    private function _get_datatables_query()
    {
        $id_user = $this->session->userdata('id_user');
        $this->db->select("*");
        $this->db->from($this->table);
        $this->db->join('admin','admin.user = surat_masuk.pembuat_surat');
        $this->db->join('tembusan_surat_masuk','tembusan_surat_masuk.surat_masuk = surat_masuk.id_surat_masuk');
        $this->db->join('jenis_surat','surat_masuk.jenis = jenis_surat.jenis');
        $this->db->join('disposisi','id_surat = id_surat_masuk');
        $this->db->join('paraf_disposisi','paraf_disposisi.id_paraf = disposisi.id_paraf');
        //$this->db->join('admin.user','id = id_paraf');
        $this->db->where('is_acc_surat','1');
        $this->db->where('is_disposisi','1');
        $this->db->where('id_pejabat',$id_user);
        $this->db->group_by('surat_masuk');

		$i = 0;
	
		foreach ($this->column_search as $item) // loop column 
		{
			if($_POST['search']['value']) // if datatable send POST for search
			{
				
				if($i===0) // first loop
				{
					$this->db->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->db->like($item, $_POST['search']['value']);
				}
				else
				{
					$this->db->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->db->group_end(); //close bracket
			}
			$i++;
		}
         
        if(isset($_POST['order'])) 
        {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } 
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }
 
    function get_datatables()
    {
        $this->_get_datatables_query();
        if($_POST['length'] != -1)
        $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }
 
    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }
 
    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }
 
}